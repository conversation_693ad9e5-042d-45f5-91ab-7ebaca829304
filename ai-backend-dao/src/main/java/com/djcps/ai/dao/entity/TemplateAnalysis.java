package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("template_analysis")
public class TemplateAnalysis extends BaseEntity {
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 执行开始时间
     */
    private Date execStartTime;
    
    /**
     * 执行结束时间
     */
    private Date execEndTime;
    
    /**
     * 查询数据
     */
    private String originData;
    
    /**
     * 结果类型：text,table,chart
     */
    private String resultType;
    
    /**
     * 结果
     */
    private String result;
}
