package com.djcps.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.dao.entity.TemplateAnalysis;
import com.djcps.ai.dao.mapper.TemplateAnalysisMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class TemplateAnalysisService extends ServiceImpl<TemplateAnalysisMapper, TemplateAnalysis> {

    /**
     * 根据模板ID分页查询分析记录
     * @param templateId 模板ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<TemplateAnalysis> getPageByTemplateId(Long templateId, int pageNum, int pageSize) {
        Page<TemplateAnalysis> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TemplateAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TemplateAnalysis::getTemplateId, templateId)
                   .orderByDesc(TemplateAnalysis::getCreateTime);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据模板ID查询所有分析记录
     * @param templateId 模板ID
     * @return 分析记录列表
     */
    public List<TemplateAnalysis> getListByTemplateId(Long templateId) {
        LambdaQueryWrapper<TemplateAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TemplateAnalysis::getTemplateId, templateId)
                   .orderByDesc(TemplateAnalysis::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据ID查询分析记录详情（包含originData和result）
     * @param id 记录ID
     * @return 分析记录详情
     */
    public TemplateAnalysis getDetailById(Long id) {
        return this.getById(id);
    }
}
