package com.djcps.ai.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.dao.entity.UserTemplate;
import com.djcps.ai.dao.mapper.UserTemplateMapper;
import com.djcps.ai.service.vo.TestTemplate;
import com.djcps.ai.service.vo.TestTemplateResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class UserTemplateService extends ServiceImpl<UserTemplateMapper, UserTemplate> {
    private final TaskInvokeMethodService taskInvokeMethodService;
    public TestTemplateResult testTemplates(TestTemplate template) {
        Long id = template.getTemplateId();
        UserTemplate userTemplate = this.getById(id);
        TestTemplateResult result = new TestTemplateResult();
        if (userTemplate == null) {
            log.error("Template with ID {} not found", id);
            result.setResult("Template not found");
            return  result;
        }
        Long methodId = userTemplate.getMethodId();
        List<Object> objects = taskInvokeMethodService.invokeById(methodId, null);
        if (CollUtil.isEmpty(objects)) {
            result.setResult("未获取到数据");
            return result;
        }
        Object first = CollUtil.getFirst(objects);
        result.setData(JSONUtil.toJsonPrettyStr(first));
        String prompt = userTemplate.getPrompt();
        String data = StrUtil.join(";", objects);
        String string = taskInvokeMethodService.invokeAnalysisMethod(0L, "template", "test", taskInvokeMethodService.getAnalysisMethod(), prompt, data, "");
        result.setResult(string);
        return result;
    }
}
