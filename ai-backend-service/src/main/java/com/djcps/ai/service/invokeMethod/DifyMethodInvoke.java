package com.djcps.ai.service.invokeMethod;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("dify")
@RequiredArgsConstructor
@Slf4j
public class DifyMethodInvoke implements InvokeService{

    private final DifyWorkflowRunService difyWorkflowRunService;
    private final static String USER_ID_TEMP = "{}_{}_{}_{}";

    @Override
    public Object invoke(InvokeParam invokeParam) {
        DifyWordflowRequestParam param = new DifyWordflowRequestParam();
        param.setUser(StrUtil.format(USER_ID_TEMP,invokeParam.getBatchNo(),invokeParam.getSkey(),invokeParam.getTaskId()));
        param.setInputs(invokeParam.getMergeParams());
        
        return difyWorkflowRunService.invokeWithRetry(param, invokeParam.getMethodParam());
    }

}
