package com.djcps.ai.service.invokeMethod;

import cn.hutool.core.util.IdUtil;
import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("sql")
@RequiredArgsConstructor
@Slf4j
public class DifySqlExecuteService implements InvokeService{

    private final DifyWorkflowRunService difyWorkflowRunService;
    @Value("${dify.key.run_sql}")
    private String executeSqlFlowKey;

    @Override
    public Object invoke(InvokeParam invokeParam) {
        DifyWordflowRequestParam param = new DifyWordflowRequestParam();
        param.setUser(IdUtil.simpleUUID());
        param.getInputs().put("sql", invokeParam.getMethodParam());

        return difyWorkflowRunService.invokeWithRetry(param, executeSqlFlowKey);
    }

}
