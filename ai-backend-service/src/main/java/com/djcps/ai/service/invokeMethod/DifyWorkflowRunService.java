package com.djcps.ai.service.invokeMethod;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.core.client.DifyWorkflowFeignClient;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import com.djcps.ai.core.vo.DifyWorkflowResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
@AllArgsConstructor
public class DifyWorkflowRunService {
    private final DifyWorkflowFeignClient difyWorkflowFeignClient;
    private final static int MAX_RETRY_ATTEMPTS = 4;
    private final static long INITIAL_RETRY_DELAY_MS = 100L;
    public Object invokeWithRetry(DifyWordflowRequestParam param, String config) {
        DifyWorkflowResponse lastResponse = null;
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                log.debug("Dify workflow invocation attempt {} of {}", attempt, MAX_RETRY_ATTEMPTS);

                DifyWorkflowResponse response = difyWorkflowFeignClient.run(param, CommonConstants.buildHeaderAuthorizationBear(config));

                // Check if the response is successful
                if (isSuccessfulResponse(response)) {
                    if (attempt > 1) {
                        log.info("Dify workflow succeeded on attempt {} after {} retries", attempt, attempt - 1);
                    }
                    return ensureUtf8Encoding(response.getData().getOutputs().getResult());
                }

                // Response received but status indicates failure
                lastResponse = response;
                log.warn("Dify workflow attempt {} failed with status: {}, error: {}",
                        attempt,
                        response.getData() != null ? response.getData().getStatus() : "unknown",
                        response.getData() != null ? response.getData().getError() : "no error details");

            } catch (Exception e) {
                lastException = e;
                log.warn("Dify workflow attempt {} failed with exception: {}", attempt, e.getMessage());
            }

            // If this wasn't the last attempt, wait before retrying
            if (attempt < MAX_RETRY_ATTEMPTS) {
                try {
                    long delayMs = INITIAL_RETRY_DELAY_MS * (long) Math.pow(2, attempt - 1);
                    log.debug("Waiting {}ms before retry attempt {}", delayMs, attempt + 1);
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Retry delay interrupted", ie);
                    break;
                }
            }
        }

        // All attempts failed
        if (lastResponse != null) {
            log.error("Dify workflow failed after {} attempts. Final response: {}", MAX_RETRY_ATTEMPTS, lastResponse);
        } else if (lastException != null) {
            log.error("Dify workflow failed after {} attempts with exception", MAX_RETRY_ATTEMPTS, lastException);
        }

        return null;
    }

    private boolean isSuccessfulResponse(DifyWorkflowResponse response) {
        return response != null
                && response.getData() != null
                && StrUtil.containsIgnoreCase(response.getData().getStatus(), "succeeded");
    }

    private Object ensureUtf8Encoding(Object result) {
        if (result instanceof String) {
            String stringResult = (String) result;

            // First decode Unicode escape sequences (\\uXXXX)
            String decodedString = decodeUnicodeEscapes(stringResult);


            // Then ensure proper UTF-8 encoding
            byte[] utf8Bytes = decodedString.getBytes(StandardCharsets.UTF_8);
            return new String(utf8Bytes, StandardCharsets.UTF_8);
        }
        return result;
    }

    /**
     * Decode Unicode escape sequences in the format \\uXXXX
     * @param input the input string potentially containing Unicode escapes
     * @return the decoded string with actual Unicode characters
     */
    private String decodeUnicodeEscapes(String input) {
        if (input == null || !input.contains("\\u")) {
            return input;
        }

        StringBuilder result = new StringBuilder();
        int i = 0;
        int length = input.length();

        while (i < length) {
            if (i + 5 < length && input.charAt(i) == '\\' && input.charAt(i + 1) == 'u') {
                try {
                    // Extract the 4-digit hex code
                    String hexCode = input.substring(i + 2, i + 6);
                    // Convert hex to integer and then to char
                    int charCode = Integer.parseInt(hexCode, 16);
                    result.append((char) charCode);
                    i += 6; // Skip the \\uXXXX sequence
                } catch (NumberFormatException e) {
                    // If parsing fails, just append the original characters
                    result.append(input.charAt(i));
                    i++;
                }
            } else {
                result.append(input.charAt(i));
                i++;
            }
        }

        return result.toString();
    }

}
