package com.djcps.ai.web.tools;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.dao.dto.TaskInvokeMethodPageDto;
import com.djcps.ai.dao.entity.TaskInvokeMethod;
import com.djcps.ai.service.TaskInvokeMethodService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 方法注册控制器
 */
@RestController
@RequestMapping("/task-invoke-method")
@Slf4j
@RequiredArgsConstructor
public class TaskInvokeMethodController {

    private final TaskInvokeMethodService taskInvokeMethodService;

    /**
     * 分页查询方法注册列表
     *
     * @param pageDto 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/list")
    public WebResultExt<PageResult<TaskInvokeMethod>> getPageList(@RequestBody TaskInvokeMethodPageDto pageDto) {
        try {
            PageResult<TaskInvokeMethod> result = taskInvokeMethodService.getPageList(pageDto);
            return new WebResultExt<>(result);
        } catch (Exception e) {
            log.error("查询方法注册列表失败", e);
            return WebResultExt.failure("查询方法注册列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询方法注册详情
     *
     * @param id 方法注册ID
     * @return 方法注册详情
     */
    @GetMapping("/{id}")
    public WebResultExt<TaskInvokeMethod> getById(@PathVariable Long id) {
        try {
            TaskInvokeMethod taskInvokeMethod = taskInvokeMethodService.getById(id);
            if (taskInvokeMethod == null) {
                return WebResultExt.failure("方法注册不存在");
            }
            return new WebResultExt<>(taskInvokeMethod);
        } catch (Exception e) {
            log.error("查询方法注册详情失败", e);
            return WebResultExt.failure("查询方法注册详情失败: " + e.getMessage());
        }
    }

    /**
     * 保存或更新方法注册
     *
     * @param taskInvokeMethod 方法注册对象
     * @return 保存结果
     */
    @PostMapping("/save")
    public WebResultExt<TaskInvokeMethod> save(@Valid @RequestBody TaskInvokeMethod taskInvokeMethod) {
        try {
            taskInvokeMethodService.saveOrUpdateMethod(taskInvokeMethod);
            return new WebResultExt<>(taskInvokeMethod);
        } catch (Exception e) {
            log.error("保存方法注册失败", e);
            return WebResultExt.failure("保存方法注册失败: " + e.getMessage());
        }
    }

    /**
     * 删除方法注册
     *
     * @param id 方法注册ID
     * @return 删除结果
     */
    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> delete(@PathVariable Long id) {
        try {
            boolean success = taskInvokeMethodService.removeById(id);
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除方法注册失败", e);
            return WebResultExt.failure("删除方法注册失败: " + e.getMessage());
        }
    }
}
