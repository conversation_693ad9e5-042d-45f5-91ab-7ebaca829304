package com.djcps.ai.web.tools;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.dao.entity.TemplateAnalysis;
import com.djcps.ai.service.TemplateAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/template-analysis")
@Slf4j
@RequiredArgsConstructor
public class TemplateAnalysisController {

    private final TemplateAnalysisService templateAnalysisService;

    /**
     * 根据模板ID分页查询分析记录（不包含originData和result）
     */
    @PostMapping("/page")
    public WebResultExt<Map<String, Object>> getPageByTemplateId(@RequestBody Map<String, Object> params) {
        try {
            Long templateId = Long.valueOf(params.get("templateId").toString());
            int pageNum = Integer.parseInt(params.getOrDefault("pageNum", 1).toString());
            int pageSize = Integer.parseInt(params.getOrDefault("pageSize", 20).toString());

            IPage<TemplateAnalysis> page = templateAnalysisService.getPageByTemplateId(templateId, pageNum, pageSize);
            
            // 清除敏感字段，减少数据传输量
            List<TemplateAnalysis> records = page.getRecords();
            records.forEach(record -> {
                record.setOriginData(null);
                record.setResult(null);
            });

            Map<String, Object> result = new HashMap<>();
            result.put("list", records);
            result.put("total", page.getTotal());
            result.put("pageNum", page.getCurrent());
            result.put("pageSize", page.getSize());

            return new WebResultExt<>(result);
        } catch (Exception e) {
            log.error("分页查询模板分析记录失败", e);
            return WebResultExt.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板ID查询所有分析记录（不包含originData和result）
     */
    @PostMapping("/list")
    public WebResultExt<List<TemplateAnalysis>> getListByTemplateId(@RequestBody Map<String, Object> params) {
        try {
            Long templateId = Long.valueOf(params.get("templateId").toString());
            List<TemplateAnalysis> list = templateAnalysisService.getListByTemplateId(templateId);
            
            // 清除敏感字段，减少数据传输量
            list.forEach(record -> {
                record.setOriginData(null);
                record.setResult(null);
            });

            return new WebResultExt<>(list);
        } catch (Exception e) {
            log.error("查询模板分析记录失败", e);
            return WebResultExt.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询分析记录详情（包含originData和result）
     */
    @PostMapping("/detail")
    public WebResultExt<TemplateAnalysis> getDetailById(@RequestBody Map<String, Object> params) {
        try {
            Long id = Long.valueOf(params.get("id").toString());
            TemplateAnalysis detail = templateAnalysisService.getDetailById(id);
            
            if (detail == null) {
                return WebResultExt.failure("记录不存在");
            }

            return new WebResultExt<>(detail);
        } catch (Exception e) {
            log.error("查询模板分析记录详情失败", e);
            return WebResultExt.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除分析记录
     */
    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> deleteById(@PathVariable Long id) {
        try {
            boolean success = templateAnalysisService.removeById(id);
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除模板分析记录失败", e);
            return WebResultExt.failure("删除失败: " + e.getMessage());
        }
    }
}
