package com.djcps.ai.web.tools;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.vo.in.IdDto;
import com.djcps.ai.dao.entity.UserTemplate;
import com.djcps.ai.service.UserTemplateService;
import com.djcps.ai.service.vo.SearchTemplate;
import com.djcps.ai.service.vo.TestTemplate;
import com.djcps.ai.service.vo.TestTemplateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/template")
@Slf4j
@RequiredArgsConstructor
public class TemplateController {

    private final UserTemplateService templateService;

    @PostMapping("/list")
    public WebResultExt<List<UserTemplate>> listTemplates(@RequestBody SearchTemplate searchTemplate) {
        LambdaQueryWrapper<UserTemplate> queryChainWrapper = new LambdaQueryWrapper<>();
        queryChainWrapper.eq(StrUtil.isNotBlank(searchTemplate.getBizType()),UserTemplate::getBizType, searchTemplate.getBizType());
        List<UserTemplate> list = templateService.list(queryChainWrapper);
        return new WebResultExt<>(list);
    }
    @PostMapping("/test")
    public WebResultExt<TestTemplateResult> testTemplates(@RequestBody TestTemplate template) {
        TestTemplateResult result = templateService.testTemplates(template);
        return new WebResultExt<>(result);
    }

    @PostMapping("/save")
    public WebResultExt<UserTemplate> saveTemplate(@RequestBody UserTemplate template) {
        try {
            // 设置用户ID
            template.setUserId("958");

            // 保存或更新模板
            templateService.saveOrUpdate(template);

            return new WebResultExt<>(template);
        } catch (Exception e) {
            log.error("保存模板失败", e);
            return WebResultExt.failure("保存模板失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> deleteTemplate(@PathVariable Long id) {
        try {
            boolean success = templateService.removeById(id);
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除模板失败", e);
            return WebResultExt.failure("删除模板失败: " + e.getMessage());
        }
    }
    @PostMapping("/getById")
    public WebResultExt<UserTemplate> getById(@RequestBody IdDto idDto) {
        try {
            UserTemplate success = templateService.getById(idDto.getId());
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("获取模板失败", e);
            return WebResultExt.failure("获取模板失败: " + e.getMessage());
        }
    }
}
