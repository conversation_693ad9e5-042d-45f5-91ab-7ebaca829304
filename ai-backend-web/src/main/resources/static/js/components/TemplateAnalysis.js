// TemplateAnalysis.js - 模板分析组件
const { useState, useEffect } = React;

function TemplateAnalysis() {
    // 状态管理
    const [templates, setTemplates] = useState([]);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [analysisRecords, setAnalysisRecords] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [recordDetail, setRecordDetail] = useState(null);
    const [loading, setLoading] = useState(false);
    const [recordsLoading, setRecordsLoading] = useState(false);
    const [detailLoading, setDetailLoading] = useState(false);
    const [isTesting, setIsTesting] = useState(false);
    const [testResult, setTestResult] = useState(null);

    // 获取模板列表
    useEffect(() => {
        fetchTemplateList();
    }, []);

    // 从后端获取模板列表
    const fetchTemplateList = async () => {
        try {
            setLoading(true);
            const requestBody = {
                bizType: null // 获取所有模板
            };
            const result = await apiRequest(API_CONFIG.TEMPLATE.LIST, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (result.success && result.data) {
                setTemplates(result.data);
            } else {
                console.error('获取模板列表失败:', result.msg);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setLoading(false);
        }
    };

    // 获取分析记录列表
    const fetchAnalysisRecords = async (templateId) => {
        try {
            setRecordsLoading(true);
            const requestBody = { templateId };
            const result = await apiRequest(API_CONFIG.TEMPLATE_ANALYSIS.LIST, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (result.success && result.data) {
                setAnalysisRecords(result.data);
            } else {
                console.error('获取分析记录失败:', result.msg);
                setAnalysisRecords([]);
            }
        } catch (err) {
            console.error('Error fetching analysis records:', err);
            setAnalysisRecords([]);
        } finally {
            setRecordsLoading(false);
        }
    };

    // 获取记录详情
    const fetchRecordDetail = async (recordId) => {
        try {
            setDetailLoading(true);
            const requestBody = { id: recordId };
            const result = await apiRequest(API_CONFIG.TEMPLATE_ANALYSIS.DETAIL, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (result.success && result.data) {
                setRecordDetail(result.data);
            } else {
                console.error('获取记录详情失败:', result.msg);
                setRecordDetail(null);
            }
        } catch (err) {
            console.error('Error fetching record detail:', err);
            setRecordDetail(null);
        } finally {
            setDetailLoading(false);
        }
    };

    // 选择模板
    const handleSelectTemplate = (template) => {
        setSelectedTemplate(template);
        setSelectedRecord(null);
        setRecordDetail(null);
        fetchAnalysisRecords(template.id);
    };

    // 选择分析记录
    const handleSelectRecord = (record) => {
        setSelectedRecord(record);
        fetchRecordDetail(record.id);
    };

    // 删除记录
    const handleDeleteRecord = async (recordId) => {
        if (!confirm('确定要删除这条分析记录吗？')) {
            return;
        }

        try {
            const result = await apiRequest(API_CONFIG.TEMPLATE_ANALYSIS.DELETE, {}, { id: recordId });
            if (result.success) {
                window.showMessage.success('删除成功');
                // 刷新记录列表
                if (selectedTemplate) {
                    fetchAnalysisRecords(selectedTemplate.id);
                }
                // 如果删除的是当前选中的记录，清空详情
                if (selectedRecord && selectedRecord.id === recordId) {
                    setSelectedRecord(null);
                    setRecordDetail(null);
                }
            } else {
                window.showMessage.error('删除失败: ' + result.msg);
            }
        } catch (err) {
            console.error('Error deleting record:', err);
            window.showMessage.error('删除失败');
        }
    };

    // 格式化时间
    const formatTime = (timeStr) => {
        if (!timeStr) return '-';
        return new Date(timeStr).toLocaleString();
    };

    // 格式化执行时长
    const formatDuration = (startTime, endTime) => {
        if (!startTime || !endTime) return '-';
        const duration = new Date(endTime) - new Date(startTime);
        return `${Math.round(duration / 1000)}秒`;
    };

    // 测试模板功能
    const handleTestTemplate = async () => {
        if (!selectedTemplate) {
            window.showMessage.error('请先选择模板');
            return;
        }

        try {
            setIsTesting(true);
            setTestResult(null);

            const result = await apiRequest(API_CONFIG.TEMPLATE.TEST, {
                method: 'POST',
                body: JSON.stringify({ templateId: selectedTemplate.id })
            });

            if (result.success) {
                setTestResult(result);
                window.showMessage.success('测试完成');
            } else {
                window.showMessage.error('测试失败: ' + result.msg);
            }
        } catch (err) {
            console.error('Error testing template:', err);
            window.showMessage.error('测试失败');
        } finally {
            setIsTesting(false);
        }
    };

    // 渲染模板选择和测试区域
    const renderTemplateSelection = () => (
        <div className="w-1/4 flex flex-col">
            <div className="mb-4">
                <h3 className="text-lg font-semibold mb-2">模板选择</h3>
                <select
                    value={selectedTemplate?.id?.toString() || ''}
                    onChange={(e) => {
                        const templateId = e.target.value;
                        console.log('Selected template ID:', templateId);
                        if (templateId) {
                            // 尝试两种方式查找模板：数字和字符串
                            let template = templates.find(t => t.id?.toString() === templateId);
                            if (!template) {
                                template = templates.find(t => t.id === parseInt(templateId));
                            }
                            console.log('Found template:', template);
                            if (template) {
                                handleSelectTemplate(template);
                            }
                        } else {
                            setSelectedTemplate(null);
                            setAnalysisRecords([]);
                            setSelectedRecord(null);
                            setRecordDetail(null);
                            setTestResult(null);
                        }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled={loading}
                >
                    <option value="">请选择模板</option>
                    {templates.map(template => (
                        <option key={template.id} value={template.id?.toString()}>
                            {template.title || '未命名模板'} ({template.bizType === 'deliver' ? '交付' : template.bizType === 'sale' ? '销售' : template.bizType === 'customer_service' ? '客户服务' : '其他'})
                        </option>
                    ))}
                </select>
            </div>

            {/* 测试按钮 */}
            <div className="mb-4">
                <button
                    onClick={handleTestTemplate}
                    disabled={!selectedTemplate || isTesting}
                    className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded transition-colors flex items-center justify-center"
                >
                    {isTesting ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            测试中...
                        </>
                    ) : (
                        '测试模板'
                    )}
                </button>
            </div>

            {/* 测试结果 */}
            {testResult && (
                <div className="flex-1 overflow-y-auto space-y-4">
                    <h4 className="font-medium text-sm">测试结果</h4>
                    
                    {/* 数据部分 */}
                    {testResult.data && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="font-medium text-sm mb-2">数据：</h5>
                            <pre className="text-xs bg-white p-3 rounded border overflow-x-auto whitespace-pre-wrap">
                                {typeof testResult.data === 'string' ? testResult.data : JSON.stringify(testResult.data, null, 2)}
                            </pre>
                        </div>
                    )}

                    {/* 分析结果部分 */}
                    {testResult.result && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="font-medium text-sm mb-2">分析结果：</h5>
                            <div className="text-sm bg-white p-3 rounded border whitespace-pre-wrap">
                                {typeof testResult.result === 'string' ? testResult.result : JSON.stringify(testResult.result, null, 2)}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {loading && (
                <div className="text-center py-4">
                    <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <p className="text-sm text-gray-500 mt-2">加载中...</p>
                </div>
            )}
        </div>
    );

    // 渲染分析记录列表
    const renderAnalysisRecords = () => (
        <div className="w-1/3 flex flex-col">
            <div className="mb-4">
                <h3 className="text-lg font-semibold">分析记录</h3>
                <p className="text-sm text-gray-500">
                    {selectedTemplate ? `模板: ${selectedTemplate.title}` : '请选择模板'}
                </p>
            </div>

            <div className="flex-1 overflow-y-auto space-y-2">
                {!selectedTemplate ? (
                    <div className="text-center py-8 text-gray-500">
                        <p>请先选择模板</p>
                    </div>
                ) : recordsLoading ? (
                    <div className="text-center py-4">
                        <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p className="text-sm text-gray-500 mt-2">加载中...</p>
                    </div>
                ) : analysisRecords.length > 0 ? (
                    analysisRecords.map(record => (
                        <div
                            key={record.id}
                            className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                selectedRecord?.id === record.id
                                    ? 'bg-blue-50 border-blue-300 shadow-sm'
                                    : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                            }`}
                            onClick={() => handleSelectRecord(record)}
                        >
                            <div className="flex justify-between items-start mb-2">
                                <span className="text-sm font-medium text-gray-900">
                                    记录 #{record.id}
                                </span>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteRecord(record.id);
                                    }}
                                    className="text-red-500 hover:text-red-700 text-xs"
                                >
                                    删除
                                </button>
                            </div>
                            <div className="space-y-1 text-xs text-gray-500">
                                <p>开始: {formatTime(record.execStartTime)}</p>
                                <p>结束: {formatTime(record.execEndTime)}</p>
                                <p>耗时: {formatDuration(record.execStartTime, record.execEndTime)}</p>
                                <p>类型: {record.resultType || 'text'}</p>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <p>暂无分析记录</p>
                    </div>
                )}
            </div>
        </div>
    );

    // 渲染记录详情
    const renderRecordDetail = () => (
        <div className="flex-1 flex flex-col">
            <div className="mb-4">
                <h3 className="text-lg font-semibold">记录详情</h3>
                <p className="text-sm text-gray-500">
                    {selectedRecord ? `记录 #${selectedRecord.id}` : '请选择分析记录'}
                </p>
            </div>

            <div className="flex-1 overflow-y-auto">
                {!selectedRecord ? (
                    <div className="text-center py-8 text-gray-500">
                        <p>请先选择分析记录</p>
                    </div>
                ) : detailLoading ? (
                    <div className="text-center py-4">
                        <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p className="text-sm text-gray-500 mt-2">加载中...</p>
                    </div>
                ) : recordDetail ? (
                    <div className="space-y-4">
                        {/* 基本信息 */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-sm mb-2">基本信息</h4>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div>
                                    <span className="text-gray-500">执行开始:</span>
                                    <span className="ml-2">{formatTime(recordDetail.execStartTime)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-500">执行结束:</span>
                                    <span className="ml-2">{formatTime(recordDetail.execEndTime)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-500">执行耗时:</span>
                                    <span className="ml-2">{formatDuration(recordDetail.execStartTime, recordDetail.execEndTime)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-500">结果类型:</span>
                                    <span className="ml-2">{recordDetail.resultType || 'text'}</span>
                                </div>
                            </div>
                        </div>

                        {/* 原始数据 */}
                        {recordDetail.originData && (
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium text-sm mb-2">原始数据</h4>
                                <pre className="text-xs bg-white p-3 rounded border overflow-x-auto">
                                    {recordDetail.originData}
                                </pre>
                            </div>
                        )}

                        {/* 分析结果 */}
                        {recordDetail.result && (
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium text-sm mb-2">分析结果</h4>
                                <div className="text-sm bg-white p-3 rounded border">
                                    {recordDetail.resultType === 'text' ? (
                                        <div className="whitespace-pre-wrap">{recordDetail.result}</div>
                                    ) : (
                                        <pre className="overflow-x-auto">{recordDetail.result}</pre>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <p>加载记录详情失败</p>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <div className="h-full flex gap-4">
            {renderTemplateSelection()}
            {renderAnalysisRecords()}
            {renderRecordDetail()}
        </div>
    );
}

// 导出组件
window.TemplateAnalysis = TemplateAnalysis;
