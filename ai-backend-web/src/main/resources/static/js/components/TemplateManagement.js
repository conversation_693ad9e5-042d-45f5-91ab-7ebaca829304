// TemplateManagement.js - 模板管理组件
const { useState, useEffect } = React;
function TemplateManagement() {
    // 状态管理
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [isEditingMethod, setIsEditingMethod] = useState(false);
    const [isEditingSql, setIsEditingSql] = useState(false);
    const [testResult, setTestResult] = useState(null);
    const [isTesting, setIsTesting] = useState(false);
    const [bizTypeFilter, setBizTypeFilter] = useState(''); // 业务类型筛选
    const [sqlFormData, setSqlFormData] = useState({ invokeParam: '' });
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        content: '获取最近一周的数据,并分析接单面积的变化',
        type: 'text',
        methodId: '',
        subBizType: 'summary',
        bizType: 'deliver'
    });
    const [apiList, setApiList] = useState([]);
    const [apiLoading, setApiLoading] = useState(false);

    // 获取模板列表
    useEffect(() => {
        fetchTemplateList();
    }, []);

    // 监听业务类型筛选变化
    useEffect(() => {
        fetchTemplateList(bizTypeFilter);
    }, [bizTypeFilter]);

    // 从后端获取模板列表（支持bizType过滤）
    const fetchTemplateList = async (bizType = '') => {
        try {
            setLoading(true);
            const requestBody = {
                bizType: bizType || null // 如果bizType为空，传null
            };
            const result = await apiRequest(API_CONFIG.TEMPLATE.LIST, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            // 检查响应格式
            if (result.success && result.data) {
                // 将后端数据格式转换为前端格式
                const mappedTemplates = result.data.map(template => ({
                    id: template.id,
                    name: template.title || '',
                    description: template.remark || '',
                    content: template.prompt || '',
                    type: 'text', // 默认类型，可以根据需要调整
                    methodId: template.methodId || '',
                    subBizType: template.subBizType || 'summary',
                    bizType: template.bizType || 'deliver',
                    createdAt: template.createTime ? new Date(template.createTime).toLocaleDateString() : '',
                    updatedAt: template.createTime ? new Date(template.createTime).toLocaleDateString() : ''
                }));

                setTemplates(mappedTemplates);
            } else {
                console.error('Invalid response format:', result);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setLoading(false);
        }
    };

    // 处理业务类型筛选变化
    const handleBizTypeFilterChange = (bizType) => {
        setBizTypeFilter(bizType);
        setSelectedTemplate(null); // 清空选中的模板
    };

    // 获取接口列表
    useEffect(() => {
        fetchApiList();
    }, []);

    // 从后端获取数据源列表
    const fetchApiList = async () => {
        try {
            setApiLoading(true);
            const requestData = {
                pageNum: 1,
                pageSize: 1000, // 获取所有数据源
                name: '',
                invokeType: '',
                useType: 'fetchData' // 只查询fetchData类型的数据源
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.LIST, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (result.success && result.data) {
                setApiList(result.data.list || []);
            } else {
                console.error('获取数据源列表失败:', result.msg);
                setApiList([]);
            }
        } catch (err) {
            console.error('Error fetching data source list:', err);
            setApiList([]);
        } finally {
            setApiLoading(false);
        }
    };


    // 创建新模板
    const handleCreateNew = () => {
        setFormData({
            name: '',
            description: '',
            content: '',
            type: 'text',
            methodId: '',
            subBizType: 'summary',
            bizType: 'deliver'
        });
        setSelectedTemplate(null);
        setIsEditing(true);
    };

    // 复制模板
    const handleCopyTemplate = (template, event) => {
        // 阻止事件冒泡，避免触发选中模板的逻辑
        if (event) {
            event.stopPropagation();
        }
        
        setFormData({
            name: template.name + ' - 副本',
            description: template.description,
            content: template.content,
            type: template.type,
            methodId: template.methodId || '',
            subBizType: template.subBizType || 'summary',
            bizType: template.bizType || 'deliver'
        });
        setSelectedTemplate(null); // 清空选中状态，表示这是新建
        setIsEditing(true);
    };

    // 编辑模板
    const handleEdit = () => {
        if (!selectedTemplate) return;

        setFormData({
            name: selectedTemplate.name,
            description: selectedTemplate.description,
            content: selectedTemplate.content,
            type: selectedTemplate.type,
            methodId: selectedTemplate.methodId || '',
            subBizType: selectedTemplate.subBizType || 'summary',
            bizType: selectedTemplate.bizType || 'deliver'
        });
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        setIsEditingMethod(false);
        if (selectedTemplate) {
            // 如果是编辑现有模板，恢复选中状态
            setSelectedTemplate(selectedTemplate);
        }
    };

    // 开始编辑数据源
    const handleEditMethod = () => {
        if (!selectedTemplate) return;
        setIsEditingMethod(true);
        // 将当前模板的methodId设置到formData中
        setFormData(prev => ({
            ...prev,
            methodId: selectedTemplate.methodId || ''
        }));
    };

    // 取消编辑数据源
    const handleCancelEditMethod = () => {
        setIsEditingMethod(false);
        // 恢复原来的methodId
        setFormData(prev => ({
            ...prev,
            methodId: selectedTemplate.methodId || ''
        }));
    };

    // 开始编辑SQL
    const handleEditSql = () => {
        if (!selectedTemplate) return;
        
        const associatedMethod = apiList.find(method => method.id === selectedTemplate.methodId);
        if (!associatedMethod) {
            window.showMessage.warning('未找到关联的数据源');
            return;
        }
        
        setSqlFormData({ invokeParam: associatedMethod.invokeParam || '' });
        setIsEditingSql(true);
    };

    // 取消编辑SQL
    const handleCancelEditSql = () => {
        setIsEditingSql(false);
        setSqlFormData({ invokeParam: '' });
    };

    // 保存SQL
    const handleSaveSql = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        const associatedMethod = apiList.find(method => method.id === selectedTemplate.methodId);
        if (!associatedMethod) {
            window.showMessage.warning('未找到关联的数据源');
            return;
        }

        try {
            // 更新数据源的invokeParam
            const methodData = {
                id: associatedMethod.id,
                name: associatedMethod.name,
                invokeType: associatedMethod.invokeType,
                invokeParam: sqlFormData.invokeParam,
                inputScheme: associatedMethod.inputScheme,
                outputScheme: associatedMethod.outputScheme,
                useType: associatedMethod.useType
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.SAVE, {
                body: JSON.stringify(methodData)
            });

            if (result.success) {
                // 重新加载数据源列表以确保数据同步
                await fetchApiList();
                setIsEditingSql(false);
                window.showMessage.success('SQL保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存SQL失败:', error);
            window.showMessage.error('保存SQL失败: ' + error.message);
        }
    };

    // 保存数据源
    const handleSaveMethod = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        try {
            // 构建要保存的模板数据，只更新数据源
            const templateData = {
                id: selectedTemplate.id,
                title: selectedTemplate.name,
                remark: selectedTemplate.description,
                prompt: selectedTemplate.content,
                methodId: formData.methodId,
                subBizType: selectedTemplate.subBizType,
                bizType: formData.bizType
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList(bizTypeFilter);

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                setIsEditingMethod(false);
                window.showMessage.success('数据源保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存数据源失败:', error);
            window.showMessage.error('保存数据源失败: ' + error.message);
        }
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // 保存模板
    const handleSave = async () => {
        // 验证必要字段：模板名称、业务类型、子业务域、业务提示词、数据源
        const errors = [];
        
        if (!formData.name.trim()) {
            errors.push('模板名称');
        }
        if (!formData.bizType) {
            errors.push('业务类型');
        }
        if (!formData.subBizType) {
            errors.push('子业务域');
        }
        if (!formData.content.trim()) {
            errors.push('业务提示词');
        }
        if (!formData.methodId) {
            errors.push('数据源');
        }
        
        if (errors.length > 0) {
            window.showMessage.warning(`请填写以下必要字段：${errors.join('、')}`);
            return;
        }

        try {
            // 构建要保存的模板数据，映射前端字段到后端字段
            const templateData = {
                id: selectedTemplate ? selectedTemplate.id : null,
                title: formData.name,
                remark: formData.description,
                prompt: formData.content,
                methodId: formData.methodId,
                subBizType: formData.subBizType,
                bizType: formData.bizType
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList(bizTypeFilter);

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                setIsEditing(false);
                window.showMessage.success('模板保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存模板失败:', error);
            window.showMessage.error('保存模板失败: ' + error.message);
        }
    };

    // 保存提示词
    const handleSavePrompt = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        if (!formData.content.trim()) {
            window.showMessage.warning('提示词内容不能为空');
            return;
        }

        try {
            // 构建要保存的模板数据，只更新提示词内容
            const templateData = {
                id: selectedTemplate.id,
                title: selectedTemplate.name,
                remark: selectedTemplate.description,
                prompt: formData.content,
                methodId: selectedTemplate.methodId,
                subBizType: selectedTemplate.subBizType,
                bizType: selectedTemplate.bizType || 'deliver'
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList(bizTypeFilter);

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                window.showMessage.success('提示词保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存提示词失败:', error);
            window.showMessage.error('保存提示词失败: ' + error.message);
        }
    };

    // 测试模板
    const handleTestTemplate = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        // 查找关联的数据源
        const associatedMethod = apiList.find(method => method.id === selectedTemplate.methodId);
        if (!associatedMethod) {
            window.showMessage.warning('请先为模板配置数据源');
            return;
        }

        if (associatedMethod.invokeType !== 'sql') {
            window.showMessage.warning('只有SQL类型的数据源才支持测试功能');
            return;
        }

        if (!formData.content.trim()) {
            window.showMessage.warning('提示词内容不能为空');
            return;
        }

        try {
            setIsTesting(true);
            setTestResult(null);

            const testRequestData = {
                templateId: selectedTemplate.id
            };

            const testResult = await apiRequest(API_CONFIG.TEMPLATE.TEST, {
                body: JSON.stringify(testRequestData)
            });

            if (testResult.success) {
                setTestResult({
                    success: true,
                    data: testResult.data,
                    message: '测试执行成功'
                });
                window.showMessage.success('模板测试完成！');
            } else {
                setTestResult({
                    success: false,
                    data: testResult.data,
                    error: testResult.msg || '测试失败',
                    message: '测试执行失败'
                });
                window.showMessage.error('模板测试失败: ' + (testResult.msg || '测试失败'));
            }
        } catch (error) {
            console.error('模板测试失败:', error);
            setTestResult({
                success: false,
                error: error.message,
                message: '测试执行失败'
            });
            window.showMessage.error('模板测试失败: ' + error.message);
        } finally {
            setIsTesting(false);
        }
    };

    // 删除模板
    const handleDelete = async () => {
        if (!selectedTemplate) return;

        const confirmed = await window.showConfirm(`确定要删除模板 "${selectedTemplate.name}" 吗？`);
        if (confirmed) {
            try {
                const result = await apiRequest(API_CONFIG.TEMPLATE.DELETE, {}, { id: selectedTemplate.id });

                if (result.success) {
                    // 重新加载模板列表以确保数据同步
                    await fetchTemplateList(bizTypeFilter);
                    setSelectedTemplate(null);
                    window.showMessage.success('模板删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除模板失败:', error);
                window.showMessage.error('删除模板失败: ' + error.message);
            }
        }
    };

    // 渲染模板详情及测试区
    const renderTemplateDetailAndTest = () => {
        if (!selectedTemplate) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p>选择一个模板查看详情</p>
                </div>
            );
        }
        // 查找关联的数据源
        const associatedMethod = apiList.find(method => method.id === selectedTemplate.methodId);
        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTemplate.name}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            编辑模板
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                        >
                            删除
                        </button>
                    </div>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                    <p>{selectedTemplate.description}</p>
                    <p className="mt-1">
                        <span className="font-medium">业务类型: </span>
                        {selectedTemplate.bizType === 'deliver' ? '交付' :
                         selectedTemplate.bizType === 'sale' ? '销售' :
                         selectedTemplate.bizType === 'customer_service' ? '客户服务' :
                         selectedTemplate.bizType || '未设置'}
                    </p>

                    {/* 数据源显示/编辑区域 */}
                    <div className="mt-2 flex items-center">
                        <span>数据源: </span>
                        {isEditingMethod ? (
                            <div className="flex items-center space-x-2 ml-2">
                                <select
                                    name="methodId"
                                    value={formData.methodId}
                                    onChange={handleInputChange}
                                    className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                                >
                                    <option value="">选择数据源</option>
                                    {apiList.map(method => (
                                        <option key={method.id} value={method.id}>
                                            {method.name} - {method.invokeType}
                                        </option>
                                    ))}
                                </select>
                                <button
                                    onClick={handleSaveMethod}
                                    className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                                >
                                    保存
                                </button>
                                <button
                                    onClick={handleCancelEditMethod}
                                    className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                                >
                                    取消
                                </button>
                            </div>
                        ) : (
                            <div className="flex items-center ml-2">
                                {associatedMethod ? (
                                    <span className="font-medium">{associatedMethod.name} - {associatedMethod.description}</span>
                                ) : (
                                    <span className="text-gray-400">未关联数据源</span>
                                )}
                                <button
                                    onClick={handleEditMethod}
                                    className="ml-2 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                                >
                                    编辑
                                </button>
                                {associatedMethod && associatedMethod.invokeType === 'sql' && (
                                    <button
                                        onClick={handleEditSql}
                                        className="ml-2 px-2 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600"
                                    >
                                        编辑SQL
                                    </button>
                                )}
                            </div>
                        )}
                    </div>

                    <p className="mt-1">子业务域: <span className="font-medium">{selectedTemplate.subBizType}</span></p>
                    <p className="mt-1">创建于: {selectedTemplate.createdAt} | 更新于: {selectedTemplate.updatedAt}</p>
                </div>
                
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务提示词（可编辑）:</label>
                    <textarea
                        className="w-full h-60 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono mb-2"
                        value={formData.content}
                        onChange={e => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    />
                </div>
                <div className="flex items-center space-x-2 mb-4">

                    {/* 保存提示词按钮 */}
                    <button
                        type="button"
                        onClick={handleSavePrompt}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        保存提示词
                    </button>

                    {/* 测试按钮，仅当invokeType为sql时显示 */}
                    {associatedMethod && associatedMethod.invokeType === 'sql' && (
                        <button
                            type="button"
                            onClick={handleTestTemplate}
                            disabled={isTesting}
                            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400"
                        >
                            {isTesting ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    测试中...
                                </>
                            ) : (
                                '测试'
                            )}
                        </button>
                    )}
                </div>

                {/* 测试结果展示区 */}
                {testResult && (
                    <div className="mt-6 border-t pt-4">
                        <h4 className="text-lg font-semibold mb-3">测试结果</h4>
                        <div className={`p-4 rounded-lg ${testResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                            <div className="flex items-center mb-2">
                                {testResult.success ? (
                                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                ) : (
                                    <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                )}
                                <span className={`font-medium ${testResult.success ? 'text-green-800' : 'text-red-800'}`}>
                                    {testResult.message}
                                </span>
                            </div>
                            
                            {testResult.data && (
                                <div className="mt-3">
                                    {/* 显示data字段 */}
                                    {testResult.data.data && (
                                        <div className="mb-4">
                                            <h5 className="font-medium text-gray-700 mb-2">数据：</h5>
                                            <pre className="bg-white p-3 rounded border text-sm overflow-auto max-h-64">
                                                {typeof testResult.data.data === 'string' ? testResult.data.data : JSON.stringify(testResult.data.data, null, 2)}
                                            </pre>
                                        </div>
                                    )}
                                    
                                    {/* 显示result字段 */}
                                    {testResult.data.result && (
                                        <div className="mb-4">
                                            <h5 className="font-medium text-gray-700 mb-2">分析结果：</h5>
                                            <pre className="bg-white p-3 rounded border text-sm overflow-auto max-h-64">
                                                {typeof testResult.data.result === 'string' ? testResult.data.result : JSON.stringify(testResult.data.result, null, 2)}
                                            </pre>
                                        </div>
                                    )}
                                    
                                    {/* 如果data和result都不存在，显示整个data对象 */}
                                    {!testResult.data.data && !testResult.data.result && (
                                        <div className="mb-4">
                                            <h5 className="font-medium text-gray-700 mb-2">测试数据：</h5>
                                            <pre className="bg-white p-3 rounded border text-sm overflow-auto max-h-64">
                                                {typeof testResult.data === 'string' ? testResult.data : JSON.stringify(testResult.data, null, 2)}
                                            </pre>
                                        </div>
                                    )}
                                </div>
                            )}
                            
                            {!testResult.success && testResult.error && (
                                <div className="mt-3">
                                    <h5 className="font-medium text-red-700 mb-2">错误信息：</h5>
                                    <div className="bg-white p-3 rounded border text-sm text-red-800">
                                        {testResult.error}
                                    </div>
                                </div>
                            )}
                            
                            <div className="mt-3 flex justify-end">
                                <button
                                    onClick={() => setTestResult(null)}
                                    className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                                >
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTemplateForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTemplate ? '编辑模板' : '创建新模板'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                    <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入模板名称"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <input
                        type="text"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入模板描述"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务类型</label>
                    <select
                        name="bizType"
                        value={formData.bizType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="deliver">交付</option>
                        <option value="sale">销售</option>
                        <option value="customer_service">客户服务</option>
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">子业务域</label>
                    <select
                        name="subBizType"
                        value={formData.subBizType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="summary">汇总</option>
                        <option value="product">产品</option>
                        <option value="area">区域</option>
                        <option value="team">团队</option>
                        <option value="customer">客户</option>
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">数据源</label>
                    <select
                        name="methodId"
                        value={formData.methodId}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">选择数据源</option>
                        {apiList.map(method => (
                            <option key={method.id} value={method.id}>
                                {method.name} - {method.invokeType}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex-1 flex flex-col">
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务提示词</label>
                    <textarea
                        name="content"
                        value={formData.content}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                        style={{ height: '500px' }}
                        placeholder="输入业务提示词"
                    />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">模板管理</h2>
                {!isEditing && (
                    <button 
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新模板
                    </button>
                )}
            </div>
            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                    {/* 模板列表 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium mb-3">模板列表</h3>
                            {/* 业务类型筛选 */}
                            <div className="mb-2">
                                <label className="block text-xs font-medium text-gray-600 mb-1">业务类型筛选:</label>
                                <select
                                    value={bizTypeFilter}
                                    onChange={(e) => handleBizTypeFilterChange(e.target.value)}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                >
                                    <option value="">全部类型</option>
                                    <option value="deliver">交付</option>
                                    <option value="sale">销售</option>
                                    <option value="customer_service">客户服务</option>
                                </select>
                            </div>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {templates.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无模板，点击"创建新模板"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {templates.map(template => (
                                        <li
                                            key={template.id}
                                            className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTemplate && selectedTemplate.id === template.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => {
                                                setSelectedTemplate(template);
                                                setFormData({
                                                    name: template.name,
                                                    description: template.description,
                                                    content: template.content,
                                                    type: template.type,
                                                    methodId: template.methodId,
                                                    subBizType: template.subBizType
                                                });
                                                setIsEditing(false);
                                                setIsEditingMethod(false);
                                            }}
                                        >
                                            <div className="flex justify-between items-start">
                                                <div className="flex-1 min-w-0">
                                                    <h4 className="font-medium">{template.name}</h4>
                                                    <p className="text-sm text-gray-500 mt-1 truncate">{template.description}</p>
                                                    <p className="text-xs text-gray-400 mt-1">更新于: {template.updatedAt}</p>
                                                </div>
                                                <button
                                                    onClick={(e) => handleCopyTemplate(template, e)}
                                                    className="ml-2 p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded transition-colors flex-shrink-0"
                                                    title="复制模板"
                                                >
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>
                    {/* 模板详情及测试区 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-2 p-6">
                        {isEditing ? renderTemplateForm() : renderTemplateDetailAndTest()}
                    </div>
                </div>
            )}
            
            {/* SQL编辑模态框 */}
            {isEditingSql && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">编辑SQL参数</h3>
                            <button
                                onClick={handleCancelEditSql}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    SQL参数 (invokeParam)
                                </label>
                                <textarea
                                    value={sqlFormData.invokeParam}
                                    onChange={(e) => setSqlFormData(prev => ({ ...prev, invokeParam: e.target.value }))}
                                    className="w-full h-96 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                    placeholder="请输入SQL参数..."
                                />
                                <p className="mt-1 text-xs text-gray-500">
                                    请输入数据源的SQL相关参数。具体格式请参考数据源配置说明。
                                </p>
                            </div>
                        </div>
                        
                        <div className="flex justify-end space-x-3 mt-6">
                            <button
                                onClick={handleCancelEditSql}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                            >
                                取消
                            </button>
                            <button
                                onClick={handleSaveSql}
                                className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
                            >
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
